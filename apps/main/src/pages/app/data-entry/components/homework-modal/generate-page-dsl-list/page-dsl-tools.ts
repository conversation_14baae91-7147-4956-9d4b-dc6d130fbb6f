import { cloneDeep, isObject } from 'lodash';
import { GenerateBusinessType } from '../homework-modal.tool';
import { dataConnectorTemplate, pageDslTemplateMap, baseGenerateAthComponentSchemaMap } from './config';
import { EspActionField, LangObject, CategoryComponentMap, EspActionMap, GenerateAthComponentType } from './type';
import { lcdpConverterManager } from './lcdp-converter/LcdpConverterManager';
import { v4 as uuidv4 } from 'uuid';
import { DslData } from 'components/page-design/components/dsl-form-render/dsl-form-render.type';
import { last } from 'lodash';
import { getDataConnectorByAction } from './data-connector-tools';
import { dictionaryDataConnectorTemplate, queryDslDataConnectorTemplate } from './config';

// 关于在这里产生dsl数据我个人是不建议的，在这里并没法取到lowcode组件的最新meta信息以及转换器，且无法复用设计器中现成的拖拽生成逻辑
// 但由于多方面原因，不得不在进入设计前就得产生dsl以及数据源
// 所以在这里，要依据 特定场景的 静态模版，加上 移植的 动态栏位组件生成逻辑 和 组件meta 以及 简化移植版的 转换器逻辑  生成dsl
// 但其实弊端很明显：1.如果dsl结构变动后需要更新模版和处理逻辑以及组件meta 2.无法和设计器复用生成逻辑 3.无法复用转换器（除非调度中心完成）
// 我更希望的是模版决定结构和自定义属性，在设计器中结合实际值和逻辑组装（同一套）
// 当然，这一点，在之后调度中心完成之后，还是有希望实现的
export const generatePageDslList = (
  formValues: {
    addPageDesignType: GenerateBusinessType;
    listActionId: string;
    formActionId?: string;
    addActionId?: string;
    editActionId?: string;
    deleteActionId?: string;
  },
  espActionMap: EspActionMap,
  lang: string = 'zh_CN',
): any[] => {
  const { addPageDesignType, listActionId, formActionId, addActionId, editActionId, deleteActionId } = formValues;
  const pageDslList = pageDslTemplateMap[addPageDesignType];
  if (!pageDslList) return null;
  // 通用数据源组装
  const commonDataConnectors = [cloneDeep(dictionaryDataConnectorTemplate), cloneDeep(queryDslDataConnectorTemplate)];
  pageDslList.forEach((pageDsl) => {
    pageDsl.dsl.dataConnectors = [...pageDsl.dsl.dataConnectors, ...commonDataConnectors];
  });

  switch (addPageDesignType) {
    case GenerateBusinessType.BASIC_TABLE: {
      const listActionEspAction = espActionMap[listActionId];
      const formActionIdEspAction = espActionMap[formActionId];
      const addActionIdEspAction = espActionMap[addActionId];
      const editActionIdEspAction = espActionMap[editActionId];
      const deleteActionIdEspAction = espActionMap[deleteActionId];

      if (!listActionEspAction || !formActionIdEspAction) return null;

      // 浏览界面
      const browsePageDsl = cloneDeep(pageDslList[0]);
      const browsePageCode = `DSL_${uuidv4()}`;
      // 编辑界面
      const editPageDsl = cloneDeep(pageDslList[1]);
      const editPageCode = `DSL_${uuidv4()}`;

      // 处理浏览界面
      let { layout: browseLayout, dataConnectors: browseDataConnectors } = browsePageDsl.dsl;
      // 由于栏位需要动态生成，所以不走静态模版，但table中的部分属性还是需要从模版中取，然后组装
      const browseTableDsl = lcdpConverterManager.toDsl(getNodeSchemaData(listActionEspAction.espActionField, lang));
      if ((browseTableDsl as DslData).type === GenerateAthComponentType.ATHENA_TABLE) {
        // 选中的action产生的是表格才会处理模版逻辑，否则不符合规则
        const slotsTemplate = browseLayout?.[0]?.['slots'];
        const operationColumnDefTemplate = last<any>(browseLayout?.[0]?.['columnDefs']);
        const queryInfoTemplate = browseLayout?.[0]?.['queryInfo'];

        // 表格绑定数据源
        queryInfoTemplate.dataConnectorId = listActionEspAction.espActionField.data_name;

        // 新增按钮
        slotsTemplate[0]['group'][0]['group'][0].targetSchema = listActionEspAction.espActionField.data_name;
        slotsTemplate[0]['group'][0]['group'][0].emitConfig.dslCode = editPageCode;

        // 删除按钮
        slotsTemplate[0]['group'][0]['group'][1].targetSchema = listActionEspAction.espActionField.data_name;
        slotsTemplate[0]['group'][0]['group'][1].action.dataConnectorId =
          deleteActionIdEspAction?.espActionField.data_name ?? '';

        // 维护按钮
        operationColumnDefTemplate.columns[0].targetSchema = listActionEspAction.espActionField.data_name;
        operationColumnDefTemplate.columns[0].emitConfig.dslCode = editPageCode;

        browseTableDsl['slots'] = slotsTemplate;
        browseTableDsl['columnDefs'].push(operationColumnDefTemplate);
        browseTableDsl['queryInfo'] = queryInfoTemplate;
      }

      browseLayout = [browseTableDsl];

      // 处理浏览界面数据源
      browseDataConnectors = [
        ...browseDataConnectors,
        getDataConnectorByAction(listActionEspAction.actionData, listActionEspAction.espActionField, lang, true, true),
      ];
      if (deleteActionIdEspAction) {
        browseDataConnectors.push(
          getDataConnectorByAction(
            deleteActionIdEspAction.actionData,
            deleteActionIdEspAction.espActionField,
            lang,
            true,
          ),
        );
      }

      // 处理编辑界面
      let { layout: editLayout, dataConnectors: editDataConnectors } = editPageDsl.dsl;
      // 由于栏位需要动态生成，所以不走静态模版
      const editFormDsl = lcdpConverterManager.toDsl(getNodeSchemaData(formActionIdEspAction.espActionField, lang));
      editFormDsl['queryInfo'] = {
        ...editLayout?.[0]?.['queryInfo'],
        dataConnectorId: formActionIdEspAction.espActionField.data_name,
      };

      // 按钮组模版
      const editButtonGroupTemplate = editLayout?.[1];
      // 保存并新增（新增）
      editButtonGroupTemplate.group[1].targetSchema = formActionIdEspAction.espActionField.data_name;
      editButtonGroupTemplate.group[1].action.dataConnectorId = addActionIdEspAction?.espActionField.data_name ?? '';
      // 保存（新增）
      editButtonGroupTemplate.group[2].targetSchema = formActionIdEspAction.espActionField.data_name;
      editButtonGroupTemplate.group[2].action.dataConnectorId = addActionIdEspAction?.espActionField.data_name ?? '';
      // 保存（编辑）
      editButtonGroupTemplate.group[3].targetSchema = formActionIdEspAction.espActionField.data_name;
      editButtonGroupTemplate.group[3].action.dataConnectorId = editActionIdEspAction?.espActionField.data_name ?? '';

      editLayout = [editFormDsl, editButtonGroupTemplate];

      // 处理编辑界面数据源
      editDataConnectors = [
        ...editDataConnectors,
        getDataConnectorByAction(formActionIdEspAction.actionData, formActionIdEspAction.espActionField, lang, true),
      ];
      if (addActionIdEspAction) {
        editDataConnectors.push(
          getDataConnectorByAction(addActionIdEspAction.actionData, addActionIdEspAction.espActionField, lang, true),
        );
      }
      if (editActionIdEspAction) {
        editDataConnectors.push(
          getDataConnectorByAction(editActionIdEspAction.actionData, editActionIdEspAction.espActionField, lang, true),
        );
      }

      return [
        {
          ...browsePageDsl,
          code: browsePageCode,
          dsl: {
            ...browsePageDsl.dsl,
            layout: browseLayout,
            dataConnectors: browseDataConnectors,
          },
        },
        {
          ...editPageDsl,
          code: editPageCode,
          dsl: {
            ...editPageDsl.dsl,
            layout: editLayout,
            dataConnectors: editDataConnectors,
          },
        },
      ];
    }
    case GenerateBusinessType.EDITABLE_TABLE: {
      const listActionEspAction = espActionMap[listActionId];
      const addActionIdEspAction = espActionMap[addActionId];
      const editActionIdEspAction = espActionMap[editActionId];
      const deleteActionIdEspAction = espActionMap[deleteActionId];

      if (!listActionEspAction) return null;

      const designPageDsl = cloneDeep(pageDslList[0]);
      const code = `DSL_${uuidv4()}`;
      let { layout, dataConnectors } = designPageDsl.dsl;

      // 由于栏位需要动态生成，所以不走静态模版，但table中的部分属性还是需要从模版中取，然后组装
      const tableDsl = lcdpConverterManager.toDsl(getNodeSchemaData(listActionEspAction.espActionField, lang));
      if ((tableDsl as DslData).type === GenerateAthComponentType.ATHENA_TABLE) {
        // 选中的action产生的是表格才会处理模版逻辑，否则不符合规则
        const slotsTemplate = layout?.[0]?.['slots'];
        const operationColumnDefTemplate = last<any>(layout?.[0]?.['columnDefs']);
        const queryInfoTemplate = layout?.[0]?.['queryInfo'];

        queryInfoTemplate.dataConnectorId = listActionEspAction.espActionField.data_name;

        // 新增
        slotsTemplate[0]['group'][0]['group'][0].targetSchema = listActionEspAction.espActionField.data_name;
        // 保存按钮
        slotsTemplate[0]['group'][0]['group'][1].targetSchema = listActionEspAction.espActionField.data_name;
        // 保存按钮-新建
        slotsTemplate[0]['group'][0]['group'][1].action.combineActions[0].dataConnectorId =
          addActionIdEspAction?.espActionField.data_name ?? '';
        // 保存按钮-保存
        slotsTemplate[0]['group'][0]['group'][1].action.combineActions[1].dataConnectorId =
          editActionIdEspAction?.espActionField.data_name ?? '';
        // 删除
        slotsTemplate[0]['group'][0]['group'][2].targetSchema = listActionEspAction.espActionField.data_name;
        slotsTemplate[0]['group'][0]['group'][2].action.dataConnectorId =
          deleteActionIdEspAction?.espActionField.data_name ?? '';

        // 操作栏位-删除
        operationColumnDefTemplate.columns[0].targetSchema = listActionEspAction.espActionField.data_name;
        operationColumnDefTemplate.columns[0].operation.dataConnectorId =
          deleteActionIdEspAction?.espActionField.data_name ?? '';

        tableDsl['slots'] = slotsTemplate;
        tableDsl['columnDefs'].push(operationColumnDefTemplate);
        tableDsl['queryInfo'] = queryInfoTemplate;
      }

      // 处理数据源
      dataConnectors = [
        ...dataConnectors,
        getDataConnectorByAction(listActionEspAction.actionData, listActionEspAction.espActionField, lang, true, true),
      ];

      if (addActionIdEspAction) {
        dataConnectors.push(
          getDataConnectorByAction(addActionIdEspAction.actionData, addActionIdEspAction.espActionField, lang, true),
        );
      }
      if (editActionIdEspAction) {
        dataConnectors.push(
          getDataConnectorByAction(editActionIdEspAction.actionData, editActionIdEspAction.espActionField, lang, true),
        );
      }
      if (deleteActionIdEspAction) {
        dataConnectors.push(
          getDataConnectorByAction(
            deleteActionIdEspAction.actionData,
            deleteActionIdEspAction.espActionField,
            lang,
            true,
          ),
        );
      }

      return [
        {
          ...designPageDsl,
          code,
          dsl: {
            ...designPageDsl.dsl,
            layout: [tableDsl],
            dataConnectors,
          },
        },
      ];
    }
    default: {
      return pageDslList;
    }
  }
};

// ============ 移植并改造自动生成逻辑 ============
export function removeLastPart(str: string) {
  const lastDotIndex = str.lastIndexOf('.');
  if (lastDotIndex === -1) return '';
  return str.substring(0, lastDotIndex);
}

// 组装placeholder
const getPlaceHolder = (type: GenerateAthComponentType, description: LangObject) => {
  if ([GenerateAthComponentType.INPUT, GenerateAthComponentType.INPUT_NUMBER].includes(type)) {
    return {
      placeholder: {
        zh_TW: '請輸入' + description['zh_TW'],
        en_US: 'Please Input ' + description['en_US'],
        zh_CN: '请输入' + description['zh_CN'],
      },
    };
  } else if (
    [
      GenerateAthComponentType.SELECT,
      GenerateAthComponentType.DATEPICKER,
      GenerateAthComponentType.TIMEPICKER,
    ].includes(type)
  ) {
    return {
      placeholder: {
        zh_TW: '請選擇' + description['zh_TW'],
        en_US: 'Please Select ' + description['en_US'],
        zh_CN: '请选择' + description['zh_CN'],
      },
    };
  } else {
    return {};
  }
};

// 获取组件的schema
const getComponentSchema = (componentName: GenerateAthComponentType, extraDslInfo = {}): any => {
  const componentSnippetDslInfo = baseGenerateAthComponentSchemaMap[componentName]?.props?.dslInfo ?? {};
  return {
    componentName: componentName,
    props: {
      dslInfo: { ...componentSnippetDslInfo, ...extraDslInfo, type: componentName },
    },
  };
};

export const getExtraDslInfo = (node: EspActionField, componentName: GenerateAthComponentType, locale = 'zh_CN') => {
  const { is_array } = node;
  if (is_array) {
    return {
      tableTitle: node.description[locale],
      lang: {
        tableTitle: node.description,
      },
      schema: node.data_name,
      path: removeLastPart(node.fullPath),
    };
  }

  if (componentName === GenerateAthComponentType.TABLE_GROUP) {
    return {
      headerName: node.description[locale],
      lang: {
        headerName: node.description,
      },
      path: removeLastPart(node.fullPath),
    };
  }

  return {
    headerName: node.description[locale],
    lang: {
      headerName: node.description,
      ...getPlaceHolder(componentName, node.description),
    },
    schema: node.data_name,
    path: removeLastPart(node.fullPath),
  };
};

// 获取组件的dslInfo
export const getNodeSchemaData = (
  node: EspActionField,
  locale = 'zh_CN',
  appointComponentName?: GenerateAthComponentType,
): any => {
  const { data_type, is_array } = node;
  const category = is_array ? 'array' : data_type;
  const componentName = appointComponentName ?? GenerateAthComponentType[CategoryComponentMap[category]];
  const children = (node?.field ?? []).filter(
    (child) => (!child.field || child.field.length === 0) && child.data_name !== 'manage_status',
  );

  const extraDslInfo = getExtraDslInfo(node, componentName, locale);
  const returnData = getComponentSchema(componentName, extraDslInfo);

  if (children.length > 0) {
    const isNeedTableGroup = componentName === GenerateAthComponentType.ATHENA_TABLE;
    returnData.children = children.map((childNode: EspActionField) => {
      if (!isNeedTableGroup) return getNodeSchemaData(childNode, locale);

      return getNodeSchemaData(
        {
          ...childNode,
          field: [childNode],
        },
        locale,
        GenerateAthComponentType.TABLE_GROUP,
      );
    });
  }

  if (componentName === GenerateAthComponentType.ATHENA_TABLE || componentName === GenerateAthComponentType.FORM_LIST) {
    returnData.children?.unshift(baseGenerateAthComponentSchemaMap[GenerateAthComponentType.DYNAMIC_OPERATION]);
  }

  return returnData;
};
