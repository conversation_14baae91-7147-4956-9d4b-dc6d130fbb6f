import { cloneDeep, isObject } from 'lodash';
import { v4 as uuidv4 } from 'uuid';
import { dataConnectorTemplate } from './config';
import { EspActionField } from './type';

// 通过action组装dataConnectors
export const getDataConnectorByAction = (
  actionData: {
    actionId: string;
    provider: string;
    serviceName: string;
  },
  espActionFields: EspActionField,
  lang: string = 'zh_CN',
  needId = false,
  isListAction = false, // 是否是作为列表数据源的action，如果是的话要按照逻辑处理分页字段
) => {
  if (!actionData || !espActionFields) return null;
  const dataConnector = cloneDeep(dataConnectorTemplate);
  dataConnector.name = espActionFields.data_name;
  dataConnector.description = espActionFields.description[lang];
  dataConnector['lang']['description'] = espActionFields.description;
  const digi_service = dataConnector?.option?.request?.headers?.find((s) => s.key === 'digi-service');
  if (isObject(digi_service.value)) {
    digi_service.value.prod = actionData.provider;
    digi_service.value.name = actionData.serviceName;
    digi_service.value = JSON.stringify(digi_service.value);
  }
  dataConnector.option.response.meta = espActionFields;
  if (needId) dataConnector.id = uuidv4();

  if (isListAction && espActionFields.is_array) {
    // 如果是 作为 listAction 且 espActionFields 是 Array 才是给  ATHENA_TABLE 用的数据源 ，才有必要 处理 分页逻辑
    dataConnector.option.postProcess.script = `
                return {
                  pageInfo: {
                      pageNo: request.body.pageInfo.pageNo,
                      totalResults: response.std_data.parameter.${espActionFields.data_name}_attr.tt_cnt,
                      hasNext: response.std_data.parameter.has_next,
                      pageSize: response.std_data.parameter.${espActionFields.data_name}_attr.cnt
                  },
                  pageData: { ${espActionFields.data_name}: response.std_data.parameter.${espActionFields.data_name} },
                }
              `;
  }

  return dataConnector;
};
