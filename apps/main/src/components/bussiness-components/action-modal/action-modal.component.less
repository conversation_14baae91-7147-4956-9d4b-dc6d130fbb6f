::ng-deep .app-action-list-modal {
 
  .ant-modal-body{
    padding: 0 !important;
    .modal-content{
      padding: 24px;
      border-top: 1px solid  #DFDFE5;;
      border-bottom: 1px solid  #DFDFE5;;
      .action-tabs{
        &.is-hide {
          display: none;
        }
      }
      .actionType {
        .ant-select {
          width: 200px;
          margin-left: 10px;
        }
      }
      .searchAction {
        width: 50%;
        margin-bottom: 10px;
      }
      .searchAction {
        margin-bottom: 10px;
      }
      .attr-table {
        height: 444px;
        position: relative;
        ::ng-deep {
          ad-table {
            // .ag-cell {
            //   line-height: 34px;
            // }
            // .athena-table-pagination {
            //   margin-top: 4px;
            // }
          }
        }
        .page-number {
          position: absolute;
          left: 0;
          top: 419px;
          font-size: 12px;
        }
      }
      .red-tip {
        color: #ea3d46;
        font-size: 13px;
      }
    }

    .modal-footer {
      display: flex;
      padding: 18px 24px;
      justify-content: center;
     
      & > .scene-select{
        display: flex;
        flex: 1;
        justify-content: left;
        align-items: center;
        .scene-select-title{
          font-size: 14px;
          color: #4D4C66;
          margin-right: 8px;
        }
        .ant-select{
          width: 280px;
          .scene-select-option{
            & > .scene-select-option-label{
              display: block;
              line-height: 24px;
              font-size: 16px;
              color: #1D1C33;
            }
            & > .scene-select-option-description{
              display: block;
              font-size: 14px;
              line-height: 20px;
              color: #8C8B99 !important;
            }
            
           
          }
        }
  
      }
  
      & > .action-btn{
        display: flex;
        flex: 1;
        justify-content: right;
        button {
          width: 88px;
          height: 32px;
          border-radius: 20px;
          margin-left: 20px;
    
          &:not(.ant-btn-primary) {
            border-color: #6a4cff;
            color: #6a4cff;
    
            &:hover {
              border-color: #5a4fee;
              color: #5a4fee;
            }
          }
        }
      }
  
  
    
    }
  }


 
}
